export default {
	// api生成设置数组，每项代表一个自动生成的规则
	generator: [
		{
			// 使用项目中已有的api.json作为输入源
			input: "src/assets/data/api.json",
			platform: "swagger",
			// 接口文件和类型文件的输出路径
			output: "src/request",
			expose: {
				request: true, // 暴露请求参数类型
				response: true, // 暴露响应类型
			},
			// 指定生成的响应数据的mediaType
			responseMediaType: "application/json",
			// 指定生成的请求体数据的bodyMediaType
			bodyMediaType: "application/json",
			// 自动检测alova版本
			version: "auto",
			// 自动检测项目类型(TS还是JS)
			type: "auto",
			// 全局导出的api名称
			global: "Apis",
			// 全局api对象挂载的宿主对象
			globalHost: "globalThis",
		},
	],
	// 自动更新接口配置
	autoUpdate: {
		// 编辑器开启时更新
		launchEditor: true,
		// 自动更新间隔，5分钟检查一次
		interval: 5 * 60 * 1000,
	},
};
