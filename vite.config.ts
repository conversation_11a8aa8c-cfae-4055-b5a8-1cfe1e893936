import {defineConfig, loadEnv} from 'vite'
import vue from '@vitejs/plugin-vue'
import {resolve} from "path"
import AutoImport from "unplugin-auto-import/vite"
import Components from "unplugin-vue-components/vite"
import Icons from "unplugin-icons/vite"
import IconsResolver from "unplugin-icons/resolver"
import {ElementPlusResolver} from 'unplugin-vue-components/resolvers'
import viteCompression from 'vite-plugin-compression'

const Timestamp = new Date().getTime(); // 随机时间戳
export default ({mode}: { mode: any }) => {
    const env = loadEnv(mode, process.cwd());
    return defineConfig({
        base: env.VITE_BASE_ROUTER_PREFIX || "/",
        server: {
            host: "0.0.0.0", // 本地ip访问
            port: 9527, // 开发端口号
            open: false, // 启动后自动打卡在浏览器访问
            proxy: {
                [env.VITE_BASE_ROUTER_PREFIX + env.VITE_BASE_API]: {
                    target: env.VITE_BASE_URL,
                    changeOrigin: true,
                    rewrite: (path) => path.replace(env.VITE_BASE_ROUTER_PREFIX + env.VITE_BASE_API, ""),
                },
            },
        },
        resolve: {
            alias: {
                "@": resolve(__dirname, "src"), // 路径别名
            },
        },
        plugins: [
            vue(),
            Icons({
                autoInstall: true,
                compiler: "vue3",
            }),
            AutoImport({
                imports: [
                    "vue",
                    "vue-router",
                    {
                        "@vueuse/core": [["*", "vueuse"]],
                        "alova/client": [["*", "alova"]],
                        "pinia": [["*", "pinia"]],
                        "vue": [["useTemplateRef", "useTemplateRef"]],
                        "echarts": [["*", "echarts"]]
                    }
                ],
                // 自动引入路径文件中暴漏的内容
                dirs: [
                    "./src/router/*",
                    "./src/store/**/*",
                    "./src/assets/**/*",
                ],
                resolvers: [
                    // ElementPlus自动引入
                    ElementPlusResolver(),
                    IconsResolver({
                        prefix: "Icon",
                    }),
                ],
            }),
            Components({
                resolvers: [
                    // ElementPlus自动引入
                    ElementPlusResolver(),
                    IconsResolver({
                        prefix: "icon", // 自动引入的Icon组件统一前缀，默认为 i，设置false为不需要前缀
                        enabledCollections: ["solar"], // 这是可选的，默认启用 Iconify 支持的所有集合['mdi']
                    }),
                ],
            }),
            // 文件打包压缩，页面访问时加载压缩过的文件，提高访问速度
            viteCompression({
                filter: /\.(js|css|json|txt|html|ico|svg)(\?.*)?$/i, // 需要压缩的文件
                threshold: 1024, // 文件容量大于这个值进行压缩
                algorithm: 'gzip', // 压缩方式
                ext: 'gz', // 后缀名
                deleteOriginFile: false, // 压缩后是否删除压缩源文件，开启后访问时403，暂没有找到解决办法
            })
        ],
        build: {
            chunkSizeWarningLimit: 1500,
            rollupOptions: {
                output: {
                    // 文件名增加随机时间戳
                    chunkFileNames: `static/js/[name].[hash]${Timestamp}.js`,
                    entryFileNames: `static/js/[name].[hash]${Timestamp}.js`,
                    assetFileNames: `static/[ext]/[name].[hash]${Timestamp}.[ext]`,
                    // 解决打包时块的大小超过限制的问题
                    manualChunks(id) {
                        if (id.includes("node_modules")) {
                            return id.toString().split("node_modules/")[1].split("/")[0].toString();
                        }
                    },
                },
            },
        }
    })
}
