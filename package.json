{"name": "my-vite-app", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vue-tsc -b && vite build", "preview": "vite preview"}, "dependencies": {"@vueuse/core": "^13.4.0", "alova": "^3.3.3", "echarts": "^5.6.0", "element-plus": "^2.10.2", "pinia": "^3.0.3", "pinia-plugin-persistedstate": "^4.3.0", "vue": "^3.5.17", "vue-router": "^4.5.1"}, "devDependencies": {"@alova/wormhole": "^1.0.7", "@iconify-json/solar": "^1.2.2", "@iconify/vue": "^5.0.0", "@types/node": "^24.0.4", "@vitejs/plugin-vue": "^6.0.0", "@vue/tsconfig": "^0.7.0", "sass": "^1.89.2", "typescript": "~5.8.3", "unplugin-auto-import": "^19.3.0", "unplugin-icons": "^22.1.0", "unplugin-vue-components": "^28.7.0", "vite": "^7.0.0", "vite-plugin-compression": "^0.5.1", "vue-tsc": "^2.2.10"}}